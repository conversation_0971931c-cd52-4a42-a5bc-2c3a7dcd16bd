{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": false, "cwd": "/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/include", "program": "/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/include/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}