#ifndef ACTUATORS_H
#define ACTUATORS_H

#include "smart_home_config.h"

/**
 * @file actuators.h
 * @brief 智能家居系统执行器模块接口定义
 * @details 本模块提供风扇控制和LED控制的统一接口
 *          直接控制Linux sysfs文件系统中的硬件设备
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

// 简化的风扇状态枚举 - 只有开/关两种状态
typedef enum {
    FAN_OFF = 0,    // 风扇关闭
    FAN_ON = 1      // 风扇开启
} fan_speed_level_t;

// 风扇状态结构体
typedef struct {
    int is_running;              // 风扇是否正在运行 (0=停止, 1=运行)
    int current_speed;           // 当前PWM速度值 (0-255)
    fan_speed_level_t level;     // 当前速度等级
} fan_status_t;

/**
 * @brief 初始化所有执行器模块
 * @details 检查风扇控制文件是否存在，初始化时关闭所有设备
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 初始化成功
 * @retval ERROR_FILE_OPEN 风扇控制文件不存在或无法访问
 */
int actuators_init(void);

/**
 * @brief 设置风扇PWM速度值
 * @details 直接向/sys/class/hwmon/hwmon1/pwm1写入速度值
 *          同时更新全局风扇状态
 * @param[in] speed PWM速度值，范围0-255 (0=停止, 255=最高速)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 设置成功
 * @retval ERROR_INVALID_PARAM 速度值超出范围(0-255)
 * @retval ERROR_FILE_WRITE 文件写入失败
 */
int set_fan_speed(int speed);

/**
 * @brief 设置风扇速度等级
 * @details 根据等级枚举设置对应的PWM值
 *          FAN_OFF=0, FAN_LOW=100, FAN_MEDIUM=180, FAN_HIGH=255
 * @param[in] level 风扇速度等级枚举值
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 设置成功
 * @retval ERROR_INVALID_PARAM 无效的等级值
 */
int set_fan_level(fan_speed_level_t level);

/**
 * @brief 开启风扇到指定等级
 * @details 如果传入FAN_OFF，则默认设置为FAN_MEDIUM
 * @param[in] level 目标速度等级，FAN_OFF时默认为FAN_MEDIUM
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 */
int fan_on(fan_speed_level_t level);

/**
 * @brief 关闭风扇
 * @details 设置PWM值为0，停止风扇运行
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 */
int fan_off(void);

/**
 * @brief 获取当前风扇状态
 * @details 返回全局维护的风扇状态信息
 * @param[out] status 输出参数，存储风扇状态结构体
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 获取成功
 * @retval ERROR_INVALID_PARAM 参数为空指针
 */
int get_fan_status(fan_status_t* status);

/**
 * @brief 格式化打印风扇状态
 * @details 以易读格式输出风扇运行状态、速度值和等级
 * @param[in] status 要打印的风扇状态结构体指针
 * @note 如果status为NULL，会输出错误信息
 */
void print_fan_status(const fan_status_t* status);

// ============================================================================
// 简化的LED控制接口 - 只有开/关两种状态
// ============================================================================

/**
 * @brief 开启LED
 * @return 成功返回SUCCESS(0)
 */
int led_on(void);

/**
 * @brief 关闭LED
 * @return 成功返回SUCCESS(0)
 */
int led_off(void);

/**
 * @brief 获取LED状态
 * @return LED状态 (0=关闭, 1=开启)
 */
int get_led_state(void);

/**
 * @brief LED闪烁 (简化版本)
 * @param[in] times 闪烁次数 (忽略)
 * @param[in] interval_ms 间隔时间 (忽略)
 * @return 成功返回SUCCESS(0)
 */
int led_blink(int times, int interval_ms);

/**
 * @brief 关闭所有LED (兼容性函数)
 * @return 成功返回SUCCESS(0)
 */
int led_all_off(void);

#endif // ACTUATORS_H
