#ifndef MQTT_CLIENT_H
#define MQTT_CLIENT_H

#include "smart_home_config.h"

/**
 * @file mqtt_client.h
 * @brief 智能家居系统MQTT客户端模块接口定义
 * @details 本模块提供MQTT通信功能，支持消息发布、订阅和接收
 *          使用libmosquitto库实现真实的MQTT通信
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

// MQTT连接状态枚举
typedef enum {
    MQTT_DISCONNECTED = 0,  // 未连接状态
    MQTT_CONNECTING,        // 正在连接中
    MQTT_CONNECTED,         // 已连接状态
    MQTT_ERROR             // 连接错误状态
} mqtt_status_t;

// MQTT消息结构体
typedef struct {
    char topic[256];        // 消息主题，最大255字符
    char payload[1024];     // 消息内容，最大1023字符
    int payload_len;        // 消息内容长度(字节)
} mqtt_message_t;

// 消息接收回调函数类型定义
typedef void (*mqtt_message_callback_t)(const mqtt_message_t* message);

/**
 * @brief 初始化MQTT客户端
 * @details 初始化libmosquitto库并创建MQTT客户端实例
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 初始化成功
 * @retval ERROR_MQTT_CONNECT 客户端创建失败
 */
int mqtt_init(void);

/**
 * @brief 连接到MQTT服务器
 * @details 连接到配置文件中指定的MQTT服务器
 *          服务器: mqtt.yyzlab.com.cn:1883
 *          客户端ID: kmlg_a7_board_01
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 连接成功
 * @retval ERROR_MQTT_CONNECT 连接失败
 */
int mqtt_connect(void);

/**
 * @brief 断开MQTT连接
 * @details 优雅地断开与MQTT服务器的连接
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 断开成功
 */
int mqtt_disconnect(void);

/**
 * @brief 发布消息到指定主题
 * @details 向指定MQTT主题发布消息，需要先建立连接
 * @param[in] topic 目标主题字符串
 * @param[in] payload 消息内容
 * @param[in] payload_len 消息内容长度(字节)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 发布成功
 * @retval ERROR_INVALID_PARAM 参数无效(空指针)
 * @retval ERROR_MQTT_CONNECT MQTT未连接
 */
int mqtt_publish(const char* topic, const char* payload, int payload_len);

/**
 * @brief 订阅MQTT主题
 * @details 订阅指定主题以接收消息，需要先建立连接
 * @param[in] topic 要订阅的主题字符串
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 订阅成功
 * @retval ERROR_INVALID_PARAM 参数无效(空指针)
 * @retval ERROR_MQTT_CONNECT MQTT未连接
 */
int mqtt_subscribe(const char* topic);

/**
 * @brief 取消订阅MQTT主题
 * @details 取消对指定主题的订阅
 * @param[in] topic 要取消订阅的主题字符串
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 取消订阅成功
 * @retval ERROR_INVALID_PARAM 参数无效(空指针)
 */
int mqtt_unsubscribe(const char* topic);

/**
 * @brief 设置消息接收回调函数
 * @details 设置当接收到MQTT消息时调用的回调函数
 * @param[in] callback 回调函数指针，可以为NULL(取消回调)
 */
void mqtt_set_message_callback(mqtt_message_callback_t callback);

/**
 * @brief 获取当前MQTT连接状态
 * @details 返回当前MQTT客户端的连接状态
 * @return MQTT连接状态枚举值
 * @retval MQTT_DISCONNECTED 未连接
 * @retval MQTT_CONNECTING 正在连接
 * @retval MQTT_CONNECTED 已连接
 * @retval MQTT_ERROR 连接错误
 */
mqtt_status_t mqtt_get_status(void);

/**
 * @brief MQTT循环处理函数
 * @details 调用libmosquitto的循环处理函数，处理网络事件
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 处理成功
 * @retval ERROR_MQTT_CONNECT 处理失败
 */
int mqtt_loop(void);

/**
 * @brief 启动MQTT后台循环线程
 * @details 创建后台线程处理MQTT消息接收和发送
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 线程启动成功
 * @retval ERROR_MQTT_CONNECT 线程创建失败
 */
int mqtt_loop_start(void);

/**
 * @brief 停止MQTT后台循环线程
 * @details 优雅地停止后台线程并等待线程结束
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 线程停止成功
 * @retval ERROR_MQTT_CONNECT 线程结束失败
 */
int mqtt_loop_stop(void);

/**
 * @brief 清理MQTT客户端资源
 * @details 停止后台线程、断开连接并清理所有资源
 */
void mqtt_cleanup(void);

/**
 * @brief 发布传感器数据到数据主题
 * @details 向MQTT_PUB_TOPIC主题发布JSON格式的传感器数据
 * @param[in] json_data JSON格式的传感器数据字符串
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 发布成功
 * @retval ERROR_INVALID_PARAM 参数无效(空指针)
 * @retval 其他错误码 mqtt_publish的返回值
 */
int mqtt_publish_sensor_data(const char* json_data);

/**
 * @brief 发布状态响应到数据主题
 * @details 向MQTT_PUB_TOPIC主题发布JSON格式的响应数据
 * @param[in] json_response JSON格式的响应数据字符串
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 发布成功
 * @retval ERROR_INVALID_PARAM 参数无效(空指针)
 * @retval 其他错误码 mqtt_publish的返回值
 */
int mqtt_publish_response(const char* json_response);

#endif // MQTT_CLIENT_H
