#ifndef SENSORS_H
#define SENSORS_H

#include "smart_home_config.h"

/**
 * @file sensors.h
 * @brief 智能家居系统传感器模块接口定义
 * @details 本模块提供温湿度、光照、电压传感器的统一接口
 *          直接从Linux sysfs文件系统读取硬件数据
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

// 传感器数据结构
typedef struct {
    float temperature;       // 温度值 (°C) - 来自iio:device0
    float humidity;          // 湿度值 (%RH) - 来自iio:device0
    int light_intensity;     // 光照强度 (lux) - 来自iio:device1
    float voltage;           // 电压值 (mV) - 来自iio:device3，用于电流检测
    unsigned long timestamp; // 数据采集时间戳 (Unix时间戳)
} sensor_data_t;

/**
 * @brief 初始化所有传感器模块
 * @details 检查传感器设备文件是否存在，验证硬件连接
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 初始化成功
 * @retval ERROR_FILE_OPEN 传感器设备文件不存在或无法访问
 */
int sensors_init(void);

/**
 * @brief 读取温度传感器数据
 * @details 从iio:device0读取温度原始值、偏移量、缩放因子，
 *          按公式计算: 温度(°C) = (raw + offset) * scale / 1000.0
 * @param[out] temperature 输出参数，存储计算后的温度值(°C)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 读取成功
 * @retval ERROR_INVALID_PARAM 参数为空指针
 * @retval ERROR_FILE_READ 文件读取失败
 */
int get_temperature(float* temperature);

/**
 * @brief 读取湿度传感器数据
 * @details 从iio:device0读取湿度原始值、偏移量、缩放因子，
 *          按公式计算: 湿度(%RH) = (raw + offset) * scale / 1000.0
 *          结果限制在0-100%范围内
 * @param[out] humidity 输出参数，存储计算后的湿度值(%RH)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 读取成功
 * @retval ERROR_INVALID_PARAM 参数为空指针
 * @retval ERROR_FILE_READ 文件读取失败
 */
int get_humidity(float* humidity);

/**
 * @brief 读取光照传感器数据
 * @details 从iio:device1的in_illuminance_input文件直接读取光照强度值
 * @param[out] light_intensity 输出参数，存储光照强度值(lux)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 读取成功
 * @retval ERROR_INVALID_PARAM 参数为空指针
 * @retval ERROR_FILE_READ 文件读取失败
 */
int get_light_intensity(int* light_intensity);

/**
 * @brief 读取电压传感器数据(用于电流检测)
 * @details 从iio:device3读取电压原始值，
 *          按公式计算: 电压(mV) = raw * 3300 / 65536
 * @param[out] voltage 输出参数，存储计算后的电压值(mV)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 读取成功
 * @retval ERROR_INVALID_PARAM 参数为空指针
 * @retval ERROR_FILE_READ 文件读取失败
 */
int get_voltage(float* voltage);

/**
 * @brief 一次性读取所有传感器数据
 * @details 依次调用各个传感器读取函数，并设置时间戳
 *          如果任一传感器读取失败，立即返回错误
 * @param[out] data 输出参数，存储所有传感器数据的结构体
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 所有传感器读取成功
 * @retval ERROR_INVALID_PARAM 参数为空指针
 * @retval 其他错误码 对应传感器的读取错误
 */
int read_all_sensors(sensor_data_t* data);

/**
 * @brief 格式化打印传感器数据
 * @details 以易读的格式输出所有传感器数据，包括时间戳
 * @param[in] data 要打印的传感器数据结构体指针
 * @note 如果data为NULL，会输出错误信息
 */
void print_sensor_data(const sensor_data_t* data);

#endif // SENSORS_H
