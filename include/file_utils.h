#ifndef FILE_UTILS_H
#define FILE_UTILS_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include "smart_home_config.h"

/**
 * @file file_utils.h
 * @brief 智能家居系统文件操作工具模块接口定义
 * @details 本模块封装Linux sysfs文件系统的读写操作
 *          提供统一的错误处理机制
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

/**
 * @brief 从sysfs文件读取浮点数值
 * @details 打开指定的sysfs文件并使用fscanf读取浮点数
 * @param[in] path sysfs文件的完整路径
 * @param[out] value 输出参数，存储读取到的浮点数值
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 读取成功
 * @retval ERROR_INVALID_PARAM 参数无效(空指针)
 * @retval ERROR_FILE_OPEN 文件打开失败
 * @retval ERROR_FILE_READ 文件读取失败
 */
int read_sysfs_float(const char* path, float* value);

/**
 * @brief 从sysfs文件读取整数值
 * @details 打开指定的sysfs文件并使用fscanf读取整数
 * @param[in] path sysfs文件的完整路径
 * @param[out] value 输出参数，存储读取到的整数值
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 读取成功
 * @retval ERROR_INVALID_PARAM 参数无效(空指针)
 * @retval ERROR_FILE_OPEN 文件打开失败
 * @retval ERROR_FILE_READ 文件读取失败
 */
int read_sysfs_int(const char* path, int* value);

/**
 * @brief 向sysfs文件写入字符串
 * @details 打开指定的sysfs文件并使用fprintf写入字符串
 * @param[in] path sysfs文件的完整路径
 * @param[in] data 要写入的字符串内容
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 写入成功
 * @retval ERROR_INVALID_PARAM 参数无效(空指针)
 * @retval ERROR_FILE_OPEN 文件打开失败
 * @retval ERROR_FILE_WRITE 文件写入失败
 */
int write_sysfs_string(const char* path, const char* data);

/**
 * @brief 向sysfs文件写入整数值
 * @details 将整数转换为字符串后调用write_sysfs_string写入
 * @param[in] path sysfs文件的完整路径
 * @param[in] value 要写入的整数值
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 写入成功
 * @retval 其他错误码 write_sysfs_string的返回值
 */
int write_sysfs_int(const char* path, int value);

/**
 * @brief 检查文件是否存在且可访问
 * @details 使用access()系统调用检查文件是否存在且可访问
 * @param[in] path 要检查的文件路径
 * @return 文件存在且可访问返回1，否则返回0
 * @retval 1 文件存在且可访问
 * @retval 0 文件不存在、无法访问或参数为空
 */
int file_exists(const char* path);

/**
 * @brief 统一的错误信息打印函数
 * @details 以标准格式向stderr输出错误信息
 *          格式: [ERROR] 函数名: 错误信息 (code: 错误码)
 * @param[in] func_name 发生错误的函数名
 * @param[in] error_msg 错误描述信息
 * @param[in] error_code 错误码(通常为负数)
 */
void print_error(const char* func_name, const char* error_msg, int error_code);

#endif // FILE_UTILS_H
