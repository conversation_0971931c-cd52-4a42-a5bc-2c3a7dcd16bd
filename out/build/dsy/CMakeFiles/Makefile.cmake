# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../../../CMakeLists.txt"
  "CMakeFiles/3.18.4/CMakeCCompiler.cmake"
  "CMakeFiles/3.18.4/CMakeSystem.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.18/Modules/CheckCSourceCompiles.cmake"
  "/usr/share/cmake-3.18/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake-3.18/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.18/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.18/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.18/Modules/FindThreads.cmake"
  "/usr/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake"
  "/usr/share/cmake-3.18/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.18/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.18/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.18/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/test_fan.dir/DependInfo.cmake"
  "CMakeFiles/test_sensors.dir/DependInfo.cmake"
  "CMakeFiles/smart_home_system.dir/DependInfo.cmake"
  )
