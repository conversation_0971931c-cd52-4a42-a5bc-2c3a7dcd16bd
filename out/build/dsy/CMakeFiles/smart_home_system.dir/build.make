# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy

# Include any dependencies generated for this target.
include CMakeFiles/smart_home_system.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/smart_home_system.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/smart_home_system.dir/flags.make

CMakeFiles/smart_home_system.dir/src/main.c.o: CMakeFiles/smart_home_system.dir/flags.make
CMakeFiles/smart_home_system.dir/src/main.c.o: ../../../src/main.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/smart_home_system.dir/src/main.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/smart_home_system.dir/src/main.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/main.c

CMakeFiles/smart_home_system.dir/src/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/smart_home_system.dir/src/main.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/main.c > CMakeFiles/smart_home_system.dir/src/main.c.i

CMakeFiles/smart_home_system.dir/src/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/smart_home_system.dir/src/main.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/main.c -o CMakeFiles/smart_home_system.dir/src/main.c.s

CMakeFiles/smart_home_system.dir/src/file_utils.c.o: CMakeFiles/smart_home_system.dir/flags.make
CMakeFiles/smart_home_system.dir/src/file_utils.c.o: ../../../src/file_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/smart_home_system.dir/src/file_utils.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/smart_home_system.dir/src/file_utils.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/file_utils.c

CMakeFiles/smart_home_system.dir/src/file_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/smart_home_system.dir/src/file_utils.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/file_utils.c > CMakeFiles/smart_home_system.dir/src/file_utils.c.i

CMakeFiles/smart_home_system.dir/src/file_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/smart_home_system.dir/src/file_utils.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/file_utils.c -o CMakeFiles/smart_home_system.dir/src/file_utils.c.s

CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o: CMakeFiles/smart_home_system.dir/flags.make
CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o: ../../../src/temp_humidity_sensor.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/temp_humidity_sensor.c

CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/temp_humidity_sensor.c > CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.i

CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/temp_humidity_sensor.c -o CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.s

CMakeFiles/smart_home_system.dir/src/light_sensor.c.o: CMakeFiles/smart_home_system.dir/flags.make
CMakeFiles/smart_home_system.dir/src/light_sensor.c.o: ../../../src/light_sensor.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/smart_home_system.dir/src/light_sensor.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/smart_home_system.dir/src/light_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/light_sensor.c

CMakeFiles/smart_home_system.dir/src/light_sensor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/smart_home_system.dir/src/light_sensor.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/light_sensor.c > CMakeFiles/smart_home_system.dir/src/light_sensor.c.i

CMakeFiles/smart_home_system.dir/src/light_sensor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/smart_home_system.dir/src/light_sensor.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/light_sensor.c -o CMakeFiles/smart_home_system.dir/src/light_sensor.c.s

CMakeFiles/smart_home_system.dir/src/current_sensor.c.o: CMakeFiles/smart_home_system.dir/flags.make
CMakeFiles/smart_home_system.dir/src/current_sensor.c.o: ../../../src/current_sensor.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/smart_home_system.dir/src/current_sensor.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/smart_home_system.dir/src/current_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/current_sensor.c

CMakeFiles/smart_home_system.dir/src/current_sensor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/smart_home_system.dir/src/current_sensor.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/current_sensor.c > CMakeFiles/smart_home_system.dir/src/current_sensor.c.i

CMakeFiles/smart_home_system.dir/src/current_sensor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/smart_home_system.dir/src/current_sensor.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/current_sensor.c -o CMakeFiles/smart_home_system.dir/src/current_sensor.c.s

CMakeFiles/smart_home_system.dir/src/fan_control.c.o: CMakeFiles/smart_home_system.dir/flags.make
CMakeFiles/smart_home_system.dir/src/fan_control.c.o: ../../../src/fan_control.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/smart_home_system.dir/src/fan_control.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/smart_home_system.dir/src/fan_control.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/fan_control.c

CMakeFiles/smart_home_system.dir/src/fan_control.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/smart_home_system.dir/src/fan_control.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/fan_control.c > CMakeFiles/smart_home_system.dir/src/fan_control.c.i

CMakeFiles/smart_home_system.dir/src/fan_control.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/smart_home_system.dir/src/fan_control.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/fan_control.c -o CMakeFiles/smart_home_system.dir/src/fan_control.c.s

CMakeFiles/smart_home_system.dir/src/data_package.c.o: CMakeFiles/smart_home_system.dir/flags.make
CMakeFiles/smart_home_system.dir/src/data_package.c.o: ../../../src/data_package.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/smart_home_system.dir/src/data_package.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/smart_home_system.dir/src/data_package.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/data_package.c

CMakeFiles/smart_home_system.dir/src/data_package.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/smart_home_system.dir/src/data_package.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/data_package.c > CMakeFiles/smart_home_system.dir/src/data_package.c.i

CMakeFiles/smart_home_system.dir/src/data_package.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/smart_home_system.dir/src/data_package.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/data_package.c -o CMakeFiles/smart_home_system.dir/src/data_package.c.s

CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o: CMakeFiles/smart_home_system.dir/flags.make
CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o: ../../../src/mqtt_client.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/mqtt_client.c

CMakeFiles/smart_home_system.dir/src/mqtt_client.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/smart_home_system.dir/src/mqtt_client.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/mqtt_client.c > CMakeFiles/smart_home_system.dir/src/mqtt_client.c.i

CMakeFiles/smart_home_system.dir/src/mqtt_client.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/smart_home_system.dir/src/mqtt_client.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/mqtt_client.c -o CMakeFiles/smart_home_system.dir/src/mqtt_client.c.s

# Object files for target smart_home_system
smart_home_system_OBJECTS = \
"CMakeFiles/smart_home_system.dir/src/main.c.o" \
"CMakeFiles/smart_home_system.dir/src/file_utils.c.o" \
"CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o" \
"CMakeFiles/smart_home_system.dir/src/light_sensor.c.o" \
"CMakeFiles/smart_home_system.dir/src/current_sensor.c.o" \
"CMakeFiles/smart_home_system.dir/src/fan_control.c.o" \
"CMakeFiles/smart_home_system.dir/src/data_package.c.o" \
"CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o"

# External object files for target smart_home_system
smart_home_system_EXTERNAL_OBJECTS =

smart_home_system: CMakeFiles/smart_home_system.dir/src/main.c.o
smart_home_system: CMakeFiles/smart_home_system.dir/src/file_utils.c.o
smart_home_system: CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o
smart_home_system: CMakeFiles/smart_home_system.dir/src/light_sensor.c.o
smart_home_system: CMakeFiles/smart_home_system.dir/src/current_sensor.c.o
smart_home_system: CMakeFiles/smart_home_system.dir/src/fan_control.c.o
smart_home_system: CMakeFiles/smart_home_system.dir/src/data_package.c.o
smart_home_system: CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o
smart_home_system: CMakeFiles/smart_home_system.dir/build.make
smart_home_system: CMakeFiles/smart_home_system.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Linking C executable smart_home_system"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/smart_home_system.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/smart_home_system.dir/build: smart_home_system

.PHONY : CMakeFiles/smart_home_system.dir/build

CMakeFiles/smart_home_system.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/smart_home_system.dir/cmake_clean.cmake
.PHONY : CMakeFiles/smart_home_system.dir/clean

CMakeFiles/smart_home_system.dir/depend:
	cd /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709 /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709 /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/smart_home_system.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/smart_home_system.dir/depend

