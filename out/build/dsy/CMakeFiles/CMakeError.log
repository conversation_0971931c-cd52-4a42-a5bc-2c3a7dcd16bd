Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake cmTC_5c427/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_5c427.dir/build.make CMakeFiles/cmTC_5c427.dir/build
gmake[1]: 进入目录“/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_5c427.dir/src.c.o
/usr/bin/arm-linux-gnueabihf-gcc   -Wall -Wextra -g -DCMAKE_HAVE_LIBC_PTHREAD -std=gnu99 -o CMakeFiles/cmTC_5c427.dir/src.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_5c427
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5c427.dir/link.txt --verbose=1
/usr/bin/arm-linux-gnueabihf-gcc  -Wall -Wextra -g -DCMAKE_HAVE_LIBC_PTHREAD CMakeFiles/cmTC_5c427.dir/src.c.o -o cmTC_5c427 
/usr/lib/gcc-cross/arm-linux-gnueabihf/10/../../../../arm-linux-gnueabihf/bin/ld: CMakeFiles/cmTC_5c427.dir/src.c.o: in function `main':
/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp/src.c:11: undefined reference to `pthread_create'
/usr/lib/gcc-cross/arm-linux-gnueabihf/10/../../../../arm-linux-gnueabihf/bin/ld: /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp/src.c:12: undefined reference to `pthread_detach'
/usr/lib/gcc-cross/arm-linux-gnueabihf/10/../../../../arm-linux-gnueabihf/bin/ld: /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp/src.c:13: undefined reference to `pthread_cancel'
/usr/lib/gcc-cross/arm-linux-gnueabihf/10/../../../../arm-linux-gnueabihf/bin/ld: /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp/src.c:14: undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
gmake[1]: *** [CMakeFiles/cmTC_5c427.dir/build.make:106：cmTC_5c427] 错误 1
gmake[1]: 离开目录“/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp”
gmake: *** [Makefile:140：cmTC_5c427/fast] 错误 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake cmTC_ed8e8/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_ed8e8.dir/build.make CMakeFiles/cmTC_ed8e8.dir/build
gmake[1]: 进入目录“/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_ed8e8.dir/CheckFunctionExists.c.o
/usr/bin/arm-linux-gnueabihf-gcc   -Wall -Wextra -g -DCHECK_FUNCTION_EXISTS=pthread_create -std=gnu99 -o CMakeFiles/cmTC_ed8e8.dir/CheckFunctionExists.c.o -c /usr/share/cmake-3.18/Modules/CheckFunctionExists.c
Linking C executable cmTC_ed8e8
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ed8e8.dir/link.txt --verbose=1
/usr/bin/arm-linux-gnueabihf-gcc  -Wall -Wextra -g -DCHECK_FUNCTION_EXISTS=pthread_create CMakeFiles/cmTC_ed8e8.dir/CheckFunctionExists.c.o -o cmTC_ed8e8  -lpthreads 
/usr/lib/gcc-cross/arm-linux-gnueabihf/10/../../../../arm-linux-gnueabihf/bin/ld: 找不到 -lpthreads
collect2: error: ld returned 1 exit status
gmake[1]: *** [CMakeFiles/cmTC_ed8e8.dir/build.make:106：cmTC_ed8e8] 错误 1
gmake[1]: 离开目录“/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/CMakeTmp”
gmake: *** [Makefile:140：cmTC_ed8e8/fast] 错误 2



