# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy

# Include any dependencies generated for this target.
include CMakeFiles/test_sensors.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/test_sensors.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_sensors.dir/flags.make

CMakeFiles/test_sensors.dir/tests/test_sensors.c.o: CMakeFiles/test_sensors.dir/flags.make
CMakeFiles/test_sensors.dir/tests/test_sensors.c.o: ../../../tests/test_sensors.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/test_sensors.dir/tests/test_sensors.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/test_sensors.dir/tests/test_sensors.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/tests/test_sensors.c

CMakeFiles/test_sensors.dir/tests/test_sensors.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/test_sensors.dir/tests/test_sensors.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/tests/test_sensors.c > CMakeFiles/test_sensors.dir/tests/test_sensors.c.i

CMakeFiles/test_sensors.dir/tests/test_sensors.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/test_sensors.dir/tests/test_sensors.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/tests/test_sensors.c -o CMakeFiles/test_sensors.dir/tests/test_sensors.c.s

CMakeFiles/test_sensors.dir/src/file_utils.c.o: CMakeFiles/test_sensors.dir/flags.make
CMakeFiles/test_sensors.dir/src/file_utils.c.o: ../../../src/file_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/test_sensors.dir/src/file_utils.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/test_sensors.dir/src/file_utils.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/file_utils.c

CMakeFiles/test_sensors.dir/src/file_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/test_sensors.dir/src/file_utils.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/file_utils.c > CMakeFiles/test_sensors.dir/src/file_utils.c.i

CMakeFiles/test_sensors.dir/src/file_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/test_sensors.dir/src/file_utils.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/file_utils.c -o CMakeFiles/test_sensors.dir/src/file_utils.c.s

CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o: CMakeFiles/test_sensors.dir/flags.make
CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o: ../../../src/temp_humidity_sensor.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/temp_humidity_sensor.c

CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/temp_humidity_sensor.c > CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.i

CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/temp_humidity_sensor.c -o CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.s

CMakeFiles/test_sensors.dir/src/light_sensor.c.o: CMakeFiles/test_sensors.dir/flags.make
CMakeFiles/test_sensors.dir/src/light_sensor.c.o: ../../../src/light_sensor.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/test_sensors.dir/src/light_sensor.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/test_sensors.dir/src/light_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/light_sensor.c

CMakeFiles/test_sensors.dir/src/light_sensor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/test_sensors.dir/src/light_sensor.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/light_sensor.c > CMakeFiles/test_sensors.dir/src/light_sensor.c.i

CMakeFiles/test_sensors.dir/src/light_sensor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/test_sensors.dir/src/light_sensor.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/light_sensor.c -o CMakeFiles/test_sensors.dir/src/light_sensor.c.s

CMakeFiles/test_sensors.dir/src/current_sensor.c.o: CMakeFiles/test_sensors.dir/flags.make
CMakeFiles/test_sensors.dir/src/current_sensor.c.o: ../../../src/current_sensor.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/test_sensors.dir/src/current_sensor.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/test_sensors.dir/src/current_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/current_sensor.c

CMakeFiles/test_sensors.dir/src/current_sensor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/test_sensors.dir/src/current_sensor.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/current_sensor.c > CMakeFiles/test_sensors.dir/src/current_sensor.c.i

CMakeFiles/test_sensors.dir/src/current_sensor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/test_sensors.dir/src/current_sensor.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/current_sensor.c -o CMakeFiles/test_sensors.dir/src/current_sensor.c.s

# Object files for target test_sensors
test_sensors_OBJECTS = \
"CMakeFiles/test_sensors.dir/tests/test_sensors.c.o" \
"CMakeFiles/test_sensors.dir/src/file_utils.c.o" \
"CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o" \
"CMakeFiles/test_sensors.dir/src/light_sensor.c.o" \
"CMakeFiles/test_sensors.dir/src/current_sensor.c.o"

# External object files for target test_sensors
test_sensors_EXTERNAL_OBJECTS =

test_sensors: CMakeFiles/test_sensors.dir/tests/test_sensors.c.o
test_sensors: CMakeFiles/test_sensors.dir/src/file_utils.c.o
test_sensors: CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o
test_sensors: CMakeFiles/test_sensors.dir/src/light_sensor.c.o
test_sensors: CMakeFiles/test_sensors.dir/src/current_sensor.c.o
test_sensors: CMakeFiles/test_sensors.dir/build.make
test_sensors: CMakeFiles/test_sensors.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking C executable test_sensors"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_sensors.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_sensors.dir/build: test_sensors

.PHONY : CMakeFiles/test_sensors.dir/build

CMakeFiles/test_sensors.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_sensors.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_sensors.dir/clean

CMakeFiles/test_sensors.dir/depend:
	cd /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709 /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709 /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/test_sensors.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_sensors.dir/depend

