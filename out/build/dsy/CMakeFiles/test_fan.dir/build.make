# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy

# Include any dependencies generated for this target.
include CMakeFiles/test_fan.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/test_fan.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_fan.dir/flags.make

CMakeFiles/test_fan.dir/tests/test_fan.c.o: CMakeFiles/test_fan.dir/flags.make
CMakeFiles/test_fan.dir/tests/test_fan.c.o: ../../../tests/test_fan.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/test_fan.dir/tests/test_fan.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/test_fan.dir/tests/test_fan.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/tests/test_fan.c

CMakeFiles/test_fan.dir/tests/test_fan.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/test_fan.dir/tests/test_fan.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/tests/test_fan.c > CMakeFiles/test_fan.dir/tests/test_fan.c.i

CMakeFiles/test_fan.dir/tests/test_fan.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/test_fan.dir/tests/test_fan.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/tests/test_fan.c -o CMakeFiles/test_fan.dir/tests/test_fan.c.s

CMakeFiles/test_fan.dir/src/file_utils.c.o: CMakeFiles/test_fan.dir/flags.make
CMakeFiles/test_fan.dir/src/file_utils.c.o: ../../../src/file_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/test_fan.dir/src/file_utils.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/test_fan.dir/src/file_utils.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/file_utils.c

CMakeFiles/test_fan.dir/src/file_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/test_fan.dir/src/file_utils.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/file_utils.c > CMakeFiles/test_fan.dir/src/file_utils.c.i

CMakeFiles/test_fan.dir/src/file_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/test_fan.dir/src/file_utils.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/file_utils.c -o CMakeFiles/test_fan.dir/src/file_utils.c.s

CMakeFiles/test_fan.dir/src/fan_control.c.o: CMakeFiles/test_fan.dir/flags.make
CMakeFiles/test_fan.dir/src/fan_control.c.o: ../../../src/fan_control.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/test_fan.dir/src/fan_control.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/test_fan.dir/src/fan_control.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/fan_control.c

CMakeFiles/test_fan.dir/src/fan_control.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/test_fan.dir/src/fan_control.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/fan_control.c > CMakeFiles/test_fan.dir/src/fan_control.c.i

CMakeFiles/test_fan.dir/src/fan_control.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/test_fan.dir/src/fan_control.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/src/fan_control.c -o CMakeFiles/test_fan.dir/src/fan_control.c.s

# Object files for target test_fan
test_fan_OBJECTS = \
"CMakeFiles/test_fan.dir/tests/test_fan.c.o" \
"CMakeFiles/test_fan.dir/src/file_utils.c.o" \
"CMakeFiles/test_fan.dir/src/fan_control.c.o"

# External object files for target test_fan
test_fan_EXTERNAL_OBJECTS =

test_fan: CMakeFiles/test_fan.dir/tests/test_fan.c.o
test_fan: CMakeFiles/test_fan.dir/src/file_utils.c.o
test_fan: CMakeFiles/test_fan.dir/src/fan_control.c.o
test_fan: CMakeFiles/test_fan.dir/build.make
test_fan: CMakeFiles/test_fan.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking C executable test_fan"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_fan.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_fan.dir/build: test_fan

.PHONY : CMakeFiles/test_fan.dir/build

CMakeFiles/test_fan.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_fan.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_fan.dir/clean

CMakeFiles/test_fan.dir/depend:
	cd /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709 /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709 /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles/test_fan.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_fan.dir/depend

