# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/test_fan.dir/all
all: CMakeFiles/test_sensors.dir/all
all: CMakeFiles/smart_home_system.dir/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall:

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/test_fan.dir/clean
clean: CMakeFiles/test_sensors.dir/clean
clean: CMakeFiles/smart_home_system.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/test_fan.dir

# All Build rule for target.
CMakeFiles/test_fan.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=10,11,12,13 "Built target test_fan"
.PHONY : CMakeFiles/test_fan.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_fan.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_fan.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles 0
.PHONY : CMakeFiles/test_fan.dir/rule

# Convenience name for target.
test_fan: CMakeFiles/test_fan.dir/rule

.PHONY : test_fan

# clean rule for target.
CMakeFiles/test_fan.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/clean
.PHONY : CMakeFiles/test_fan.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_sensors.dir

# All Build rule for target.
CMakeFiles/test_sensors.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=14,15,16,17,18,19 "Built target test_sensors"
.PHONY : CMakeFiles/test_sensors.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_sensors.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_sensors.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles 0
.PHONY : CMakeFiles/test_sensors.dir/rule

# Convenience name for target.
test_sensors: CMakeFiles/test_sensors.dir/rule

.PHONY : test_sensors

# clean rule for target.
CMakeFiles/test_sensors.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/clean
.PHONY : CMakeFiles/test_sensors.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/smart_home_system.dir

# All Build rule for target.
CMakeFiles/smart_home_system.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9 "Built target smart_home_system"
.PHONY : CMakeFiles/smart_home_system.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/smart_home_system.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/smart_home_system.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles 0
.PHONY : CMakeFiles/smart_home_system.dir/rule

# Convenience name for target.
smart_home_system: CMakeFiles/smart_home_system.dir/rule

.PHONY : smart_home_system

# clean rule for target.
CMakeFiles/smart_home_system.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/clean
.PHONY : CMakeFiles/smart_home_system.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

