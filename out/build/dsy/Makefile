# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/out/build/dsy/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named test_fan

# Build rule for target.
test_fan: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_fan
.PHONY : test_fan

# fast build rule for target.
test_fan/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/build
.PHONY : test_fan/fast

#=============================================================================
# Target rules for targets named test_sensors

# Build rule for target.
test_sensors: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_sensors
.PHONY : test_sensors

# fast build rule for target.
test_sensors/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/build
.PHONY : test_sensors/fast

#=============================================================================
# Target rules for targets named smart_home_system

# Build rule for target.
smart_home_system: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 smart_home_system
.PHONY : smart_home_system

# fast build rule for target.
smart_home_system/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/build
.PHONY : smart_home_system/fast

src/current_sensor.o: src/current_sensor.c.o

.PHONY : src/current_sensor.o

# target to build an object file
src/current_sensor.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/current_sensor.c.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/current_sensor.c.o
.PHONY : src/current_sensor.c.o

src/current_sensor.i: src/current_sensor.c.i

.PHONY : src/current_sensor.i

# target to preprocess a source file
src/current_sensor.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/current_sensor.c.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/current_sensor.c.i
.PHONY : src/current_sensor.c.i

src/current_sensor.s: src/current_sensor.c.s

.PHONY : src/current_sensor.s

# target to generate assembly for a file
src/current_sensor.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/current_sensor.c.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/current_sensor.c.s
.PHONY : src/current_sensor.c.s

src/data_package.o: src/data_package.c.o

.PHONY : src/data_package.o

# target to build an object file
src/data_package.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/data_package.c.o
.PHONY : src/data_package.c.o

src/data_package.i: src/data_package.c.i

.PHONY : src/data_package.i

# target to preprocess a source file
src/data_package.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/data_package.c.i
.PHONY : src/data_package.c.i

src/data_package.s: src/data_package.c.s

.PHONY : src/data_package.s

# target to generate assembly for a file
src/data_package.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/data_package.c.s
.PHONY : src/data_package.c.s

src/fan_control.o: src/fan_control.c.o

.PHONY : src/fan_control.o

# target to build an object file
src/fan_control.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/src/fan_control.c.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/fan_control.c.o
.PHONY : src/fan_control.c.o

src/fan_control.i: src/fan_control.c.i

.PHONY : src/fan_control.i

# target to preprocess a source file
src/fan_control.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/src/fan_control.c.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/fan_control.c.i
.PHONY : src/fan_control.c.i

src/fan_control.s: src/fan_control.c.s

.PHONY : src/fan_control.s

# target to generate assembly for a file
src/fan_control.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/src/fan_control.c.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/fan_control.c.s
.PHONY : src/fan_control.c.s

src/file_utils.o: src/file_utils.c.o

.PHONY : src/file_utils.o

# target to build an object file
src/file_utils.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/src/file_utils.c.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/file_utils.c.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/file_utils.c.o
.PHONY : src/file_utils.c.o

src/file_utils.i: src/file_utils.c.i

.PHONY : src/file_utils.i

# target to preprocess a source file
src/file_utils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/src/file_utils.c.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/file_utils.c.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/file_utils.c.i
.PHONY : src/file_utils.c.i

src/file_utils.s: src/file_utils.c.s

.PHONY : src/file_utils.s

# target to generate assembly for a file
src/file_utils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/src/file_utils.c.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/file_utils.c.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/file_utils.c.s
.PHONY : src/file_utils.c.s

src/light_sensor.o: src/light_sensor.c.o

.PHONY : src/light_sensor.o

# target to build an object file
src/light_sensor.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/light_sensor.c.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/light_sensor.c.o
.PHONY : src/light_sensor.c.o

src/light_sensor.i: src/light_sensor.c.i

.PHONY : src/light_sensor.i

# target to preprocess a source file
src/light_sensor.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/light_sensor.c.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/light_sensor.c.i
.PHONY : src/light_sensor.c.i

src/light_sensor.s: src/light_sensor.c.s

.PHONY : src/light_sensor.s

# target to generate assembly for a file
src/light_sensor.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/light_sensor.c.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/light_sensor.c.s
.PHONY : src/light_sensor.c.s

src/main.o: src/main.c.o

.PHONY : src/main.o

# target to build an object file
src/main.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/main.c.o
.PHONY : src/main.c.o

src/main.i: src/main.c.i

.PHONY : src/main.i

# target to preprocess a source file
src/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/main.c.i
.PHONY : src/main.c.i

src/main.s: src/main.c.s

.PHONY : src/main.s

# target to generate assembly for a file
src/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/main.c.s
.PHONY : src/main.c.s

src/mqtt_client.o: src/mqtt_client.c.o

.PHONY : src/mqtt_client.o

# target to build an object file
src/mqtt_client.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o
.PHONY : src/mqtt_client.c.o

src/mqtt_client.i: src/mqtt_client.c.i

.PHONY : src/mqtt_client.i

# target to preprocess a source file
src/mqtt_client.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/mqtt_client.c.i
.PHONY : src/mqtt_client.c.i

src/mqtt_client.s: src/mqtt_client.c.s

.PHONY : src/mqtt_client.s

# target to generate assembly for a file
src/mqtt_client.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/mqtt_client.c.s
.PHONY : src/mqtt_client.c.s

src/temp_humidity_sensor.o: src/temp_humidity_sensor.c.o

.PHONY : src/temp_humidity_sensor.o

# target to build an object file
src/temp_humidity_sensor.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o
.PHONY : src/temp_humidity_sensor.c.o

src/temp_humidity_sensor.i: src/temp_humidity_sensor.c.i

.PHONY : src/temp_humidity_sensor.i

# target to preprocess a source file
src/temp_humidity_sensor.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.i
.PHONY : src/temp_humidity_sensor.c.i

src/temp_humidity_sensor.s: src/temp_humidity_sensor.c.s

.PHONY : src/temp_humidity_sensor.s

# target to generate assembly for a file
src/temp_humidity_sensor.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smart_home_system.dir/build.make CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.s
.PHONY : src/temp_humidity_sensor.c.s

tests/test_fan.o: tests/test_fan.c.o

.PHONY : tests/test_fan.o

# target to build an object file
tests/test_fan.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/tests/test_fan.c.o
.PHONY : tests/test_fan.c.o

tests/test_fan.i: tests/test_fan.c.i

.PHONY : tests/test_fan.i

# target to preprocess a source file
tests/test_fan.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/tests/test_fan.c.i
.PHONY : tests/test_fan.c.i

tests/test_fan.s: tests/test_fan.c.s

.PHONY : tests/test_fan.s

# target to generate assembly for a file
tests/test_fan.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fan.dir/build.make CMakeFiles/test_fan.dir/tests/test_fan.c.s
.PHONY : tests/test_fan.c.s

tests/test_sensors.o: tests/test_sensors.c.o

.PHONY : tests/test_sensors.o

# target to build an object file
tests/test_sensors.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/tests/test_sensors.c.o
.PHONY : tests/test_sensors.c.o

tests/test_sensors.i: tests/test_sensors.c.i

.PHONY : tests/test_sensors.i

# target to preprocess a source file
tests/test_sensors.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/tests/test_sensors.c.i
.PHONY : tests/test_sensors.c.i

tests/test_sensors.s: tests/test_sensors.c.s

.PHONY : tests/test_sensors.s

# target to generate assembly for a file
tests/test_sensors.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_sensors.dir/build.make CMakeFiles/test_sensors.dir/tests/test_sensors.c.s
.PHONY : tests/test_sensors.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... smart_home_system"
	@echo "... test_fan"
	@echo "... test_sensors"
	@echo "... src/current_sensor.o"
	@echo "... src/current_sensor.i"
	@echo "... src/current_sensor.s"
	@echo "... src/data_package.o"
	@echo "... src/data_package.i"
	@echo "... src/data_package.s"
	@echo "... src/fan_control.o"
	@echo "... src/fan_control.i"
	@echo "... src/fan_control.s"
	@echo "... src/file_utils.o"
	@echo "... src/file_utils.i"
	@echo "... src/file_utils.s"
	@echo "... src/light_sensor.o"
	@echo "... src/light_sensor.i"
	@echo "... src/light_sensor.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/mqtt_client.o"
	@echo "... src/mqtt_client.i"
	@echo "... src/mqtt_client.s"
	@echo "... src/temp_humidity_sensor.o"
	@echo "... src/temp_humidity_sensor.i"
	@echo "... src/temp_humidity_sensor.s"
	@echo "... tests/test_fan.o"
	@echo "... tests/test_fan.i"
	@echo "... tests/test_fan.s"
	@echo "... tests/test_sensors.o"
	@echo "... tests/test_sensors.i"
	@echo "... tests/test_sensors.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

