{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "smart_home_system", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "2smart_home_system::@6890427a1f51a3e7e1df", "jsonFile": "target-2smart_home_system-Debug-e1a67d50288dfcece84b.json", "name": "2smart_home_system", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_fan::@6890427a1f51a3e7e1df", "jsonFile": "target-test_fan-Debug-230c2fc09ad96d600d6c.json", "name": "test_fan", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_sensors::@6890427a1f51a3e7e1df", "jsonFile": "target-test_sensors-Debug-c723732a0ad428e1aa25.json", "name": "test_sensors", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/build", "source": "/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709"}, "version": {"major": 2, "minor": 1}}