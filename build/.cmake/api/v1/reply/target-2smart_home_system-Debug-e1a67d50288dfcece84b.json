{"artifacts": [{"path": "2smart_home_system"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 27, "parent": 0}, {"command": 1, "file": 0, "line": 24, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -g -g"}, {"fragment": "-std=gnu99"}], "includes": [{"backtrace": 2, "path": "/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/include"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "id": "2smart_home_system::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wall -Wextra -g -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}], "language": "C"}, "name": "2smart_home_system", "nameOnDisk": "2smart_home_system", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/file_utils.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/temp_humidity_sensor.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/light_sensor.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/current_sensor.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fan_control.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/data_package.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/mqtt_client.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}