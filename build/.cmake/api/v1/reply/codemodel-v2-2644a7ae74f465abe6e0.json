{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "smart_home_system", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "2smart_home_system::@6890427a1f51a3e7e1df", "jsonFile": "target-2smart_home_system-Debug-6f74165ac046d57b9de0.json", "name": "2smart_home_system", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709/build", "source": "/home/<USER>/smart_home_system_final_20250831_1709/smart_home_system_final_20250831_1709"}, "version": {"major": 2, "minor": 1}}