#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "actuators.h"

int main(void) {
    printf("=== Fan and LED Test Program ===\n");
    
#ifdef SIMULATION_MODE
    printf("Running in SIMULATION MODE\n");
#else
    printf("Running in HARDWARE MODE\n");
#endif
    printf("=================================\n\n");

    // 初始化执行器
    printf("Initializing actuators...\n");
    int ret = actuators_init();
    if (ret != SUCCESS) {
        printf("ERROR: Failed to initialize actuators (code: %d)\n", ret);
        return EXIT_FAILURE;
    }
    printf("Actuators initialized successfully\n\n");

    // 测试风扇控制
    printf("=== Fan Control Test ===\n");
    
    fan_status_t status;
    
    printf("Testing fan OFF...\n");
    ret = fan_off();
    if (ret == SUCCESS) {
        get_fan_status(&status);
        print_fan_status(&status);
    } else {
        printf("ERROR: Failed to turn off fan (code: %d)\n", ret);
    }
    sleep(2);

    printf("\nTesting fan LOW speed...\n");
    ret = set_fan_level(FAN_LOW);
    if (ret == SUCCESS) {
        get_fan_status(&status);
        print_fan_status(&status);
    } else {
        printf("ERROR: Failed to set fan to low speed (code: %d)\n", ret);
    }
    sleep(3);

    printf("\nTesting fan MEDIUM speed...\n");
    ret = set_fan_level(FAN_MEDIUM);
    if (ret == SUCCESS) {
        get_fan_status(&status);
        print_fan_status(&status);
    } else {
        printf("ERROR: Failed to set fan to medium speed (code: %d)\n", ret);
    }
    sleep(3);

    printf("\nTesting fan HIGH speed...\n");
    ret = set_fan_level(FAN_HIGH);
    if (ret == SUCCESS) {
        get_fan_status(&status);
        print_fan_status(&status);
    } else {
        printf("ERROR: Failed to set fan to high speed (code: %d)\n", ret);
    }
    sleep(3);

    printf("\nTesting custom fan speed (128)...\n");
    ret = set_fan_speed(128);
    if (ret == SUCCESS) {
        get_fan_status(&status);
        print_fan_status(&status);
    } else {
        printf("ERROR: Failed to set custom fan speed (code: %d)\n", ret);
    }
    sleep(3);

    printf("\nTurning fan OFF...\n");
    fan_off();
    sleep(1);

    // 测试LED控制
    printf("\n=== LED Control Test ===\n");
    
    printf("Testing individual LED control...\n");
    for (int led = 1; led <= 3; led++) {
        printf("LED %d ON\n", led);
        set_led_state(led, 1);
        sleep(1);
        printf("LED %d OFF\n", led);
        set_led_state(led, 0);
        sleep(1);
    }

    printf("\nTesting LED blink (3 times, 500ms interval)...\n");
    ret = led_blink(3, 500);
    if (ret != SUCCESS) {
        printf("ERROR: Failed to blink LEDs (code: %d)\n", ret);
    }
    sleep(2);

    printf("\nTesting LED blink (5 times, 200ms interval)...\n");
    ret = led_blink(5, 200);
    if (ret != SUCCESS) {
        printf("ERROR: Failed to blink LEDs (code: %d)\n", ret);
    }
    sleep(2);

    // 确保所有设备都关闭
    printf("\nTurning off all devices...\n");
    fan_off();
    led_all_off();

    printf("\n=== Fan and LED Test Completed ===\n");
    return EXIT_SUCCESS;
}
