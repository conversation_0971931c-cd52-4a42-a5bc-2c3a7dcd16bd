#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "sensors.h"

int main(void) {
    printf("=== Sensor Test Program ===\n");
    
#ifdef SIMULATION_MODE
    printf("Running in SIMULATION MODE\n");
#else
    printf("Running in HARDWARE MODE\n");
#endif
    printf("============================\n\n");

    // 初始化传感器
    printf("Initializing sensors...\n");
    int ret = sensors_init();
    if (ret != SUCCESS) {
        printf("ERROR: Failed to initialize sensors (code: %d)\n", ret);
        return EXIT_FAILURE;
    }
    printf("Sensors initialized successfully\n\n");

    // 测试单个传感器读取
    printf("=== Individual Sensor Tests ===\n");
    
    float temperature;
    ret = get_temperature(&temperature);
    if (ret == SUCCESS) {
        printf("Temperature: %.2f°C\n", temperature);
    } else {
        printf("ERROR: Failed to read temperature (code: %d)\n", ret);
    }

    float humidity;
    ret = get_humidity(&humidity);
    if (ret == SUCCESS) {
        printf("Humidity: %.2f%%RH\n", humidity);
    } else {
        printf("ERROR: Failed to read humidity (code: %d)\n", ret);
    }

    int light_intensity;
    ret = get_light_intensity(&light_intensity);
    if (ret == SUCCESS) {
        printf("Light Intensity: %d lux\n", light_intensity);
    } else {
        printf("ERROR: Failed to read light intensity (code: %d)\n", ret);
    }

    float voltage;
    ret = get_voltage(&voltage);
    if (ret == SUCCESS) {
        printf("Voltage: %.2f mV\n", voltage);
    } else {
        printf("ERROR: Failed to read voltage (code: %d)\n", ret);
    }

    printf("\n=== Continuous Reading Test ===\n");
    printf("Reading all sensors every 2 seconds (Press Ctrl+C to stop)...\n\n");

    sensor_data_t data;
    int count = 0;
    
    while (count < 10) { // 限制测试次数
        ret = read_all_sensors(&data);
        if (ret == SUCCESS) {
            printf("Reading #%d:\n", count + 1);
            print_sensor_data(&data);
            printf("\n");
        } else {
            printf("ERROR: Failed to read all sensors (code: %d)\n", ret);
        }
        
        count++;
        sleep(2);
    }

    printf("=== Sensor Test Completed ===\n");
    return EXIT_SUCCESS;
}
