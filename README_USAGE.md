# 智能家居系统开发使用文档

## 📖 项目概述

### 系统简介
本项目是一个基于A7开发板的智能家居控制系统，采用C语言开发，实现了完整的物联网设备功能。系统通过Linux sysfs文件系统直接操作硬件设备，使用MQTT协议进行云端通信，符合M4规范的JSON数据格式。

### 核心功能
- ✅ **环境监测** - 实时采集温度、湿度、光照强度、电压等环境数据
- ✅ **智能控制** - 基于环境数据的自动化设备控制逻辑
- ✅ **云端通信** - 通过MQTT协议实现与云端平台的双向通信
- ✅ **远程控制** - 接收并执行来自云端的设备控制指令
- ✅ **标准兼容** - 完全符合M4规范的JSON数据格式

### 技术特点
- 🔧 **纯硬件实现** - 直接操作Linux sysfs文件系统，无模拟代码
- 🏗️ **模块化设计** - 清晰的分层架构，便于维护和扩展
- 🌐 **标准协议** - 使用MQTT标准协议和M4规范JSON格式
- ⚡ **实时响应** - 多线程设计，支持并发数据处理
- 🛡️ **错误处理** - 完善的错误检测和恢复机制

## 🏗️ 系统架构

### 分层架构设计
```
┌─────────────────────────────────────────────────────────────────┐
│                     应用层 (Application Layer)                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   主程序    │  │  自动控制   │  │  信号处理   │              │
│  │  (main.c)   │  │    逻辑     │  │    模块     │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│                     服务层 (Service Layer)                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │  传感器管理 │  │  执行器管理 │  │  数据封装   │              │
│  │   模块      │  │    模块     │  │    模块     │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
│  ┌─────────────┐  ┌─────────────┐                               │
│  │  MQTT通信   │  │  文件操作   │                               │
│  │    模块     │  │    工具     │                               │
│  └─────────────┘  └─────────────┘                               │
├─────────────────────────────────────────────────────────────────┤
│                     接口层 (Interface Layer)                    │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Linux sysfs 文件系统接口                       │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     硬件层 (Hardware Layer)                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │  传感器设备 │  │  执行器设备 │  │  网络接口   │              │
│  │    硬件     │  │    硬件     │  │    硬件     │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
```

### 数据流向图
```
环境数据 → 传感器硬件 → sysfs文件 → 传感器模块 → 数据结构 → JSON封装 → MQTT发布 → 云端平台
                                                                              ↓
用户界面 ← 执行器硬件 ← sysfs文件 ← 执行器模块 ← 控制逻辑 ← JSON解析 ← MQTT接收 ← 控制指令
```

## 📁 项目文件结构

### 完整目录树
```
smart_home_system/                          # 项目根目录
├── include/                                # 头文件目录 (6个文件)
│   ├── smart_home_config.h                 # 系统全局配置参数
│   ├── file_utils.h                        # Linux sysfs文件操作接口
│   ├── sensors.h                           # 传感器模块统一接口
│   ├── actuators.h                         # 执行器模块统一接口
│   ├── data_package.h                      # JSON数据封装接口
│   └── mqtt_client.h                       # MQTT网络通信接口
├── src/                                    # 源代码目录 (8个文件)
│   ├── main.c                              # 主程序入口和系统控制逻辑
│   ├── file_utils.c                        # Linux sysfs文件读写实现
│   ├── temp_humidity_sensor.c              # 温湿度传感器数据采集
│   ├── light_sensor.c                      # 光照传感器数据采集
│   ├── current_sensor.c                    # 电压传感器数据采集
│   ├── fan_control.c                       # 风扇PWM控制和LED控制
│   ├── data_package.c                      # JSON数据封装和解析
│   └── mqtt_client.c                       # MQTT客户端通信实现
├── tests/                                  # 测试程序目录 (2个文件)
│   ├── test_sensors.c                      # 传感器功能单元测试
│   └── test_fan.c                          # 执行器功能单元测试
├── build/                                  # 构建输出目录 (编译时生成)
│   ├── smart_home_system                   # 主程序可执行文件
│   ├── test_sensors                        # 传感器测试程序
│   └── test_fan                            # 执行器测试程序
├── CMakeLists.txt                          # CMake构建配置文件
├── A7_DEPLOYMENT_GUIDE.md                  # A7开发板部署指南
├── HARDWARE_SUBMISSION_README.md           # 硬件版本提交说明
└── README_USAGE.md                         # 本开发使用文档
```

### 模块依赖关系
```
main.c (主程序)
├── sensors.h (传感器接口)
│   ├── temp_humidity_sensor.c (温湿度)
│   ├── light_sensor.c (光照)
│   └── current_sensor.c (电压)
├── actuators.h (执行器接口)
│   └── fan_control.c (风扇+LED)
├── data_package.h (数据封装)
│   └── data_package.c (JSON处理)
├── mqtt_client.h (MQTT通信)
│   └── mqtt_client.c (网络通信)
└── file_utils.h (文件操作)
    └── file_utils.c (sysfs读写)
```

## 🚀 快速开始

### 环境要求

#### 硬件要求
- **开发板**: A7开发板 (ARM Cortex-A7)
- **传感器**: 温湿度传感器、光照传感器、电压传感器
- **执行器**: PWM风扇、LED指示灯
- **网络**: 以太网或WiFi连接

#### 软件要求
- **操作系统**: Linux (Ubuntu 18.04+ 推荐)
- **编译器**: GCC 7.5+ (支持C99标准)
- **构建工具**: CMake 3.10+
- **依赖库**: libmosquitto-dev

### 1. 环境配置

```bash
# 安装开发工具
sudo apt-get update
sudo apt-get install build-essential cmake git

# 安装MQTT库
sudo apt-get install libmosquitto-dev

# 验证安装
gcc --version
cmake --version
pkg-config --modversion libmosquitto
```

### 2. 代码准备

```bash
# 解压代码包
tar -xzf smart_home_system_hardware_only_*.tar.gz
cd smart_home_system_hardware_only_*/

# 启用MQTT功能 (在A7开发板上)
# 编辑 src/mqtt_client.c，取消注释所有mosquitto相关代码
# 编辑 CMakeLists.txt，启用mosquitto库链接
```

### 3. 编译系统

```bash
# 创建并进入构建目录
mkdir -p build && cd build

# 配置CMake
cmake ..

# 编译项目
make

# 验证编译结果
ls -la smart_home_system test_sensors test_fan
```

## 🎯 功能详细说明

### 1. 传感器数据采集系统

#### 温湿度传感器 (iio:device0)
- **设备路径**: `/sys/bus/iio/devices/iio:device0/`
- **数据文件**:
  - `in_temp_raw` - 温度原始值
  - `in_temp_offset` - 温度偏移量
  - `in_temp_scale` - 温度缩放因子
  - `in_humidityrelative_raw` - 湿度原始值
  - `in_humidityrelative_scale` - 湿度缩放因子
- **计算公式**:
  - 温度(°C) = (raw + offset) × scale ÷ 1000.0
  - 湿度(%RH) = (raw + offset) × scale ÷ 1000.0
- **技术规格**:
  - 温度范围: -40°C ~ +85°C
  - 湿度范围: 0% ~ 100%RH
  - 精度: ±0.5°C, ±3%RH
  - 采集间隔: 5秒

#### 光照传感器 (iio:device1)
- **设备路径**: `/sys/bus/iio/devices/iio:device1/`
- **数据文件**: `in_illuminance_input`
- **数据处理**: 直接读取，无需转换
- **技术规格**:
  - 测量范围: 0 ~ 65535 lux
  - 输出单位: 勒克斯(lux)
  - 响应时间: <1秒

#### 电压传感器 (iio:device3)
- **设备路径**: `/sys/bus/iio/devices/iio:device3/`
- **数据文件**: `in_voltage1_raw`
- **计算公式**: 电压(mV) = raw × 3300 ÷ 65536
- **技术规格**:
  - 测量范围: 0 ~ 3.3V
  - 输出单位: 毫伏(mV)
  - 分辨率: 16位ADC
  - 用途: 电源监测、电流检测

### 2. 执行器控制系统

#### 风扇PWM控制
- **控制文件**: `/sys/class/hwmon/hwmon1/pwm1`
- **控制方式**: PWM占空比控制
- 只写0和1状态，关闭和开启，不需要led灯



### 4. MQTT网络通信系统

#### 连接配置
- **MQTT服务器**: mqtt.yyzlab.com.cn:9002
- **客户端ID**: 80a5c2c665957efaa2853a17422a2690(必须唯一)
- **发布主题**: 1756266842788/AIOTSIM2Device (设备→云端)
- **订阅主题**: 1756266842788/Device2AIOTSIM (云端→设备)
- **保活时间**: 60秒
- **QoS等级**: 0 (最多一次传递)

#### JSON数据格式 (M4规范)

**发布数据格式** (设备→云端):
```json
{
  "tem": 25.60, "hum": 45.20, "id": 0,
  "light": 350.00, "id": 1,
  "voltage": 2150.50, "id": 3,
  "fan": true, "id": 0
}
```

**设备ID映射表**:
| 设备类型 | ID | 设备路径 | 说明 |
|----------|----|---------|----- |
| 温湿度传感器 | 0 | iio:device0 | 温度和湿度数据 |
| 光照传感器 | 1 | iio:device1 | 光照强度数据 |
| 电压传感器 | 3 | iio:device3 | 电压监测数据 |
| 风扇执行器 | 0 | hwmon1/pwm1 | 风扇控制状态 |

**接收指令格式** (云端→设备):
```json
{
  "fan": true, "id": 0,
  "lamp": false, "id": 0
}
```

**指令说明**:
- `fan`: true(开启风扇) / false(关闭风扇)
- `lamp`: true(开启LED) / false(关闭LED)
- `id`: 设备标识符 (风扇和LED都是0)

#### 通信时序
- **数据发布**: 每10秒发布一次传感器数据
- **指令接收**: 实时接收并立即执行
- **连接保活**: 每60秒发送心跳包
- **重连机制**: 连接断开时自动重连

## ⚙️ 系统配置

### 主要配置参数

所有配置参数集中在 `include/smart_home_config.h` 文件中：

#### 硬件设备路径配置
```c
// 传感器设备路径
#define TEMP_HUMIDITY_DEVICE_PATH "/sys/bus/iio/devices/iio:device0"
#define LIGHT_SENSOR_DEVICE_PATH "/sys/bus/iio/devices/iio:device1"
#define CURRENT_SENSOR_DEVICE_PATH "/sys/bus/iio/devices/iio:device3"

// 执行器设备路径
#define FAN_CONTROL_PATH "/sys/class/hwmon/hwmon1/pwm1"
#define LED1_PATH "/sys/class/leds/led1/brightness"
#define LED2_PATH "/sys/class/leds/led2/brightness"
#define LED3_PATH "/sys/class/leds/led3/brightness"
```

## 📊 系统运行示例

### 正常启动输

### 数据采集和发布示例

```
[DATA] Sensor data collected:
=== Sensor Data ===
Timestamp: 1756615194
Temperature: 25.30°C        # 真实温度传感器数据
Humidity: 45.20%RH          # 真实湿度传感器数据
Light Intensity: 350 lux   # 真实光照传感器数据
Voltage: 2150.50 mV         # 真实电压传感器数据
==================

[MQTT PUBLISH] Topic: 1756266842788/AIOTSIM2Device
[MQTT PUBLISH] Payload (98 bytes): {"tem":25.30,"hum":45.20,"id":0,"light":350.00,"id":1,"voltage":2150.50,"id":3,"fan":false,"id":0}
----------------------------------------
```

```
# 温度控制触发
[DATA] Temperature: 29
[AUTO] Temperature 29.50°C > 28.00°C, turning on fan
[CONTROL] Setting fan speed to 180 (MEDIUM)

=== Fan Status ===
Running: YES
Speed: 180/255
Level: MEDIUM
==================

# 湿度警告触发
[DATA] Humidity: 75.20%RH
[AUTO] Humidity 75.20%RH > 70.00%RH, LED warning activated
[CONTROL] LED blinking 5 times with 300ms interval
```

### 远程控制示例

```
[MQTT RECEIVE] Topic: 1756266842788/Device2AIOTSIM
[MQTT RECEIVE] Payload: {"fan":true,"id":0,"lamp":false,"id":0}
----------------------------------------

[CONTROL] Processing remote command:
[CONTROL] Fan command: ON (speed: 180)
[CONTROL] LED command: OFF

[RESPONSE] Control command executed successfully

[MQTT PUBLISH] Topic: 1756266842788/AIOTSIM2Device
[MQTT PUBLISH] Payload (45 bytes): {"status":"success","message":"Commands executed"}
----------------------------------------
```

## 🔧 故障排除指南

### 常见编译错误

#### 1. 缺少MQTT库
**错误信息**: `fatal error: mosquitto.h: No such file or directory`
```bash
# 解决方案
sudo apt-get install libmosquitto-dev
```

#### 2. 链接错误
**错误信息**: `undefined reference to mosquitto_*`
```bash
# 检查CMakeLists.txt中的库链接配置
# 确保启用了: target_link_libraries(smart_home_system mosquitto Threads::Threads)
```

### 常见运行时错误

#### 1. 传感器文件不存在
**错误信息**: `Temperature sensor not found`
```bash
# 检查硬件连接和设备文件
ls -la /sys/bus/iio/devices/iio:device0/
ls -la /sys/bus/iio/devices/iio:device1/
ls -la /sys/bus/iio/devices/iio:device3/
```

#### 2. 执行器控制失败
**错误信息**: `Failed to open file for writing`
```bash
# 设置设备文件权限
sudo chmod 666 /sys/class/hwmon/hwmon1/pwm1
sudo chmod 666 /sys/class/leds/led*/brightness
```

#### 3. MQTT连接失败
**错误信息**: `Failed to connect to MQTT broker`
```bash
# 检查网络连接
ping mqtt.yyzlab.com.cn
telnet mqtt.yyzlab.com.cn 1883

# 检查防火墙设置
sudo ufw allow 1883
```

### A7开发板部署注意事项

1. **库安装**: 确保在A7开发板上安装了libmosquitto-dev
2. **代码修改**: 取消注释src/mqtt_client.c中的mosquitto相关代码
3. **权限配置**: 确保有足够权限访问设备文件
4. **网络配置**: 确保能连接到MQTT服务器

## 🚀 扩展开发指南

### 添加新传感器

1. **创建传感器源文件** (如 `src/new_sensor.c`)
2. **在sensors.h中添加接口声明**
3. **在read_all_sensors()中调用新传感器函数**
4. **更新JSON封装格式**

### 添加新执行器

1. **在fan_control.c中添加控制函数**
2. **在actuators.h中声明新接口**
3. **在主程序中集成控制逻辑**
4. **更新MQTT指令解析**

3. 

### 扩展通信协议

1. **修改data_package.c中的JSON格式**
2. **更新MQTT主题配置**
3. **适配新的数据格式**

## 📞 技术支持

### 开发支持
- **代码结构**: 查看各模块的头文件注释
- **API文档**: 参考函数注释中的详细说明
- **示例代码**: 参考tests/目录下的测试程序

### 部署支持
- **部署指南**: 查看 `A7_DEPLOYMENT_GUIDE.md`
- **硬件配置**: 检查设备文件和权限配置
- **网络配置**: 验证MQTT服务器连接

### 问题排查步骤
1. **查看系统日志** - 程序会输出详细的错误信息
2. **检查硬件状态** - 验证传感器和执行器连接
3. **验证网络连接** - 确保MQTT服务器可达
4. **检查配置参数** - 确认所有配置正确

---

**项目状态**: 开发完成，纯硬件实现 ✅
**文档版本**: v1.0.0
**最后更新**: 2025年8月31日
**维护状态**: 活跃维护中
