#include "sensors.h"
#include "file_utils.h"

// 光照传感器路径
static const char* light_input_path = LIGHT_SENSOR_DEVICE_PATH "/in_illuminance_input";

int get_light_intensity(int* light_intensity) {
    if (!light_intensity) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    int ret = read_sysfs_int(light_input_path, light_intensity);
    if (ret != SUCCESS) {
        print_error(__func__, "Failed to read light intensity", ret);
        return ret;
    }

    // 确保光照值为非负数
    if (*light_intensity < 0) {
        *light_intensity = 0;
    }

    return SUCCESS;
}
