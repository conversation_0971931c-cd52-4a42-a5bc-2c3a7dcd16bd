#include "mqtt_client.h"
#include "file_utils.h"
#include <stdio.h>
#include <string.h>
#include <unistd.h>

// 简化的MQTT状态管理
static mqtt_status_t g_mqtt_status = MQTT_DISCONNECTED;
static mqtt_message_callback_t g_message_callback = NULL;

// ============================================================================
// 核心MQTT功能实现 - 简化版本
// ============================================================================

int mqtt_init(void) {
    printf("[INFO] MQTT client initialized (simplified mode)\n");
    printf("[INFO] Server: %s:%d\n", MQTT_HOST, MQTT_PORT);
    printf("[INFO] Client ID: %s\n", MQTT_CLIENT_ID);
    g_mqtt_status = MQTT_DISCONNECTED;
    return SUCCESS;
}

int mqtt_connect(void) {
    printf("[INFO] MQTT connecting (simplified mode)...\n");
    g_mqtt_status = MQTT_CONNECTED;
    printf("[INFO] MQTT connected successfully\n");
    return SUCCESS;
}

int mqtt_disconnect(void) {
    printf("[INFO] MQTT disconnected (simplified mode)\n");
    g_mqtt_status = MQTT_DISCONNECTED;
    return SUCCESS;
}

int mqtt_publish(const char* topic, const char* payload, int payload_len) {
    if (!topic || !payload) {
        return ERROR_INVALID_PARAM;
    }

    printf("[MQTT PUBLISH] Topic: %s\n", topic);
    printf("[MQTT PUBLISH] Payload (%d bytes): %.*s\n", payload_len, payload_len, payload);
    printf("----------------------------------------\n");
    return SUCCESS;
}

int mqtt_subscribe(const char* topic) {
    if (!topic) {
        return ERROR_INVALID_PARAM;
    }

    printf("[INFO] Subscribed to topic: %s (simplified mode)\n", topic);
    return SUCCESS;
}

int mqtt_unsubscribe(const char* topic) {
    if (!topic) return ERROR_INVALID_PARAM;
    printf("[INFO] Unsubscribed from topic: %s (simplified mode)\n", topic);
    return SUCCESS;
}

int mqtt_loop_start(void) {
    printf("[INFO] MQTT loop started (simplified mode)\n");
    return SUCCESS;
}

int mqtt_loop_stop(void) {
    printf("[INFO] MQTT loop stopped (simplified mode)\n");
    return SUCCESS;
}

int mqtt_loop(void) {
    return SUCCESS;
}

void mqtt_set_message_callback(mqtt_message_callback_t callback) {
    g_message_callback = callback;
    printf("[INFO] MQTT message callback set\n");
}

mqtt_status_t mqtt_get_status(void) {
    return g_mqtt_status;
}

void mqtt_cleanup(void) {
    g_mqtt_status = MQTT_DISCONNECTED;
    printf("[INFO] MQTT client cleaned up (simplified mode)\n");
}

int mqtt_publish_sensor_data(const char* json_data) {
    if (!json_data) return ERROR_INVALID_PARAM;
    return mqtt_publish(MQTT_PUB_TOPIC, json_data, strlen(json_data));
}

int mqtt_publish_response(const char* json_response) {
    if (!json_response) return ERROR_INVALID_PARAM;
    return mqtt_publish(MQTT_PUB_TOPIC, json_response, strlen(json_response));
}
