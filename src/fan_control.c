#include "actuators.h"
#include "file_utils.h"
#include <unistd.h>

// 全局风扇状态
static fan_status_t g_fan_status = {0, 0, FAN_OFF};

int actuators_init(void) {
    // 检查风扇控制文件是否存在
    if (!file_exists(FAN_CONTROL_PATH)) {
        print_error(__func__, "Fan control file not found", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    // 初始化时关闭风扇
    int ret = fan_off();
    if (ret != SUCCESS) {
        return ret;
    }

    printf("[INFO] Actuators initialized\n");
    return SUCCESS;
}

int set_fan_speed(int speed) {
    if (speed < 0 || speed > 255) {
        print_error(__func__, "Invalid fan speed", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    int ret = write_sysfs_int(FAN_CONTROL_PATH, speed);
    if (ret != SUCCESS) {
        print_error(__func__, "Failed to set fan speed", ret);
        return ret;
    }

    // 简化的风扇状态更新
    g_fan_status.current_speed = speed;
    g_fan_status.is_running = (speed > 0) ? 1 : 0;
    g_fan_status.level = (speed > 0) ? FAN_ON : FAN_OFF;

    return SUCCESS;
}

int set_fan_level(fan_speed_level_t level) {
    if (level == FAN_OFF) {
        return set_fan_speed(0);
    } else {
        return set_fan_speed(180); // 简化为固定速度
    }
}

int fan_on(fan_speed_level_t level) {
    (void)level; // 忽略等级参数
    return set_fan_speed(180); // 固定中速
}

int fan_off(void) {
    return set_fan_speed(0);
}

int get_fan_status(fan_status_t* status) {
    if (!status) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    *status = g_fan_status;
    return SUCCESS;
}

void print_fan_status(const fan_status_t* status) {
    if (!status) {
        printf("[ERROR] Invalid fan status pointer\n");
        return;
    }

    printf("=== Fan Status ===\n");
    printf("Running: %s\n", status->is_running ? "ON" : "OFF");
    printf("Speed: %d/255\n", status->current_speed);
    printf("==================\n");
}

// ============================================================================
// 简化的LED控制 - 只有开/关两种状态
// ============================================================================

static int g_led_state = 0; // 0=关闭, 1=开启

int led_on(void) {
    printf("[INFO] LED turned ON\n");
    g_led_state = 1;
    return SUCCESS;
}

int led_off(void) {
    printf("[INFO] LED turned OFF\n");
    g_led_state = 0;
    return SUCCESS;
}

int get_led_state(void) {
    return g_led_state;
}

// 兼容性函数 - 保持接口不变
int led_all_off(void) {
    return led_off();
}

int led_blink(int times, int interval_ms) {
    (void)times;
    (void)interval_ms;
    printf("[INFO] LED blink (simplified mode)\n");
    return led_on();
}
