#include "file_utils.h"

int read_sysfs_float(const char* path, float* value) {
    if (!path || !value) {
        print_error(__func__, "Invalid parameters", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    FILE* file = fopen(path, "r");
    if (!file) {
        print_error(__func__, "Failed to open file", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    if (fscanf(file, "%f", value) != 1) {
        print_error(__func__, "Failed to read float value", ERROR_FILE_READ);
        fclose(file);
        return ERROR_FILE_READ;
    }

    fclose(file);
    return SUCCESS;
}

int read_sysfs_int(const char* path, int* value) {
    if (!path || !value) {
        print_error(__func__, "Invalid parameters", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    FILE* file = fopen(path, "r");
    if (!file) {
        print_error(__func__, "Failed to open file", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    if (fscanf(file, "%d", value) != 1) {
        print_error(__func__, "Failed to read int value", ERROR_FILE_READ);
        fclose(file);
        return ERROR_FILE_READ;
    }

    fclose(file);
    return SUCCESS;
}

int write_sysfs_string(const char* path, const char* data) {
    if (!path || !data) {
        print_error(__func__, "Invalid parameters", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    FILE* file = fopen(path, "w");
    if (!file) {
        print_error(__func__, "Failed to open file for writing", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    if (fprintf(file, "%s", data) < 0) {
        print_error(__func__, "Failed to write string", ERROR_FILE_WRITE);
        fclose(file);
        return ERROR_FILE_WRITE;
    }

    fclose(file);
    return SUCCESS;
}

int write_sysfs_int(const char* path, int value) {
    char buffer[32];
    snprintf(buffer, sizeof(buffer), "%d", value);
    return write_sysfs_string(path, buffer);
}

int file_exists(const char* path) {
    if (!path) {
        return 0;
    }

    return (access(path, F_OK) == 0) ? 1 : 0;
}

void print_error(const char* func_name, const char* error_msg, int error_code) {
    fprintf(stderr, "[ERROR] %s: %s (code: %d)\n", func_name, error_msg, error_code);
}
