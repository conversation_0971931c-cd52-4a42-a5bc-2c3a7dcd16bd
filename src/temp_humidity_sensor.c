#include "sensors.h"
#include "file_utils.h"
#include <time.h>

// 温湿度传感器相关路径
static const char* temp_raw_path = TEMP_HUMIDITY_DEVICE_PATH "/in_temp_raw";
static const char* temp_offset_path = TEMP_HUMIDITY_DEVICE_PATH "/in_temp_offset";
static const char* temp_scale_path = TEMP_HUMIDITY_DEVICE_PATH "/in_temp_scale";

static const char* humidity_raw_path = TEMP_HUMIDITY_DEVICE_PATH "/in_humidityrelative_raw";
static const char* humidity_offset_path = TEMP_HUMIDITY_DEVICE_PATH "/in_humidityrelative_offset";
static const char* humidity_scale_path = TEMP_HUMIDITY_DEVICE_PATH "/in_humidityrelative_scale";

int get_temperature(float* temperature) {
    if (!temperature) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    float raw = 0.0f, offset = 0.0f, scale = 0.0f;
    int ret;

    // 读取原始值
    ret = read_sysfs_float(temp_raw_path, &raw);
    if (ret != SUCCESS) {
        print_error(__func__, "Failed to read temperature raw value", ret);
        return ret;
    }

    // 读取偏移量（可能不存在，使用默认值0）
    ret = read_sysfs_float(temp_offset_path, &offset);
    if (ret != SUCCESS) {
        offset = 0.0f; // 使用默认偏移量
    }

    // 读取缩放因子
    ret = read_sysfs_float(temp_scale_path, &scale);
    if (ret != SUCCESS) {
        print_error(__func__, "Failed to read temperature scale value", ret);
        return ret;
    }

    // 根据文档公式计算实际温度值
    // 温度(℃) = (读取的in_temp_raw值 + 读取的in_temp_offset值) * 读取的in_temp_scale值 / 1000.0
    *temperature = (raw + offset) * scale / 1000.0f;

    return SUCCESS;
}

int get_humidity(float* humidity) {
    if (!humidity) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    float raw = 0.0f, offset = 0.0f, scale = 0.0f;
    int ret;

    // 读取原始值
    ret = read_sysfs_float(humidity_raw_path, &raw);
    if (ret != SUCCESS) {
        print_error(__func__, "Failed to read humidity raw value", ret);
        return ret;
    }

    // 读取偏移量（可能不存在，使用默认值0）
    ret = read_sysfs_float(humidity_offset_path, &offset);
    if (ret != SUCCESS) {
        offset = 0.0f; // 使用默认偏移量
    }

    // 读取缩放因子
    ret = read_sysfs_float(humidity_scale_path, &scale);
    if (ret != SUCCESS) {
        print_error(__func__, "Failed to read humidity scale value", ret);
        return ret;
    }

    // 根据文档公式计算实际湿度值
    // 湿度(%RH) = (读取的in_humidityrelative_raw值 + 读取的in_humidityrelative_offset值) * 读取的in_humidityrelative_scale值 / 1000.0
    *humidity = (raw + offset) * scale / 1000.0f;

    // 确保湿度值在合理范围内 (0-100%)
    if (*humidity < 0.0f) *humidity = 0.0f;
    if (*humidity > 100.0f) *humidity = 100.0f;

    return SUCCESS;
}

int sensors_init(void) {
    // 检查传感器文件是否存在
    if (!file_exists(temp_raw_path)) {
        print_error(__func__, "Temperature sensor not found", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    if (!file_exists(humidity_raw_path)) {
        print_error(__func__, "Humidity sensor not found", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    printf("[INFO] Temperature and humidity sensors initialized\n");
    return SUCCESS;
}

int read_all_sensors(sensor_data_t* data) {
    if (!data) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    int ret;

    // 读取温度
    ret = get_temperature(&data->temperature);
    if (ret != SUCCESS) {
        return ret;
    }

    // 读取湿度
    ret = get_humidity(&data->humidity);
    if (ret != SUCCESS) {
        return ret;
    }

    // 读取光照强度
    ret = get_light_intensity(&data->light_intensity);
    if (ret != SUCCESS) {
        return ret;
    }

    // 读取电压
    ret = get_voltage(&data->voltage);
    if (ret != SUCCESS) {
        return ret;
    }

    // 设置时间戳
    data->timestamp = (unsigned long)time(NULL);

    return SUCCESS;
}

void print_sensor_data(const sensor_data_t* data) {
    if (!data) {
        printf("[ERROR] Invalid sensor data pointer\n");
        return;
    }

    printf("=== Sensor Data ===\n");
    printf("Timestamp: %lu\n", data->timestamp);
    printf("Temperature: %.2f°C\n", data->temperature);
    printf("Humidity: %.2f%%RH\n", data->humidity);
    printf("Light Intensity: %d lux\n", data->light_intensity);
    printf("Voltage: %.2f mV\n", data->voltage);
    printf("==================\n");
}
