#include "sensors.h"
#include "file_utils.h"

// 电流传感器路径（实际是电压ADC）
static const char* voltage_raw_path = CURRENT_SENSOR_DEVICE_PATH "/in_voltage1_raw";

int get_voltage(float* voltage) {
    if (!voltage) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    float raw = 0.0f;
    int ret = read_sysfs_float(voltage_raw_path, &raw);
    if (ret != SUCCESS) {
        print_error(__func__, "Failed to read voltage raw value", ret);
        return ret;
    }

    // 根据文档公式计算实际电压值(mV)
    // 电压(mV) = 读取的in_voltage1_raw值 * 3300 / (1 << 16)
    // 其中 (1 << 16) = 65536
    *voltage = raw * 3300.0f / 65536.0f;

    return SUCCESS;
}
