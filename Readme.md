智能家居系统开发文档 (个人任务部分)
项目名称: 昆明理工大学物联网生产实习 - 智能家居系统

负责人: (Your Name)

负责模块:

温湿度传感器数据采集

光照传感器数据采集

电流传感器数据采集

风扇状态控制

数据整合与云端上报

1. 项目目标
本任务旨在开发一个运行在A7开发板上的C语言应用程序。该程序需要能够：

实时采集：周期性地从温湿度、光照、电流传感器读取数据。

设备控制：根据预设逻辑或云端指令控制风扇的启停。

数据封装：将采集到的所有传感器数据封装成JSON格式的字符串。

云端通信：通过MQTT协议将封装好的JSON数据包发布（Publish）到指定的云服务器（元宇宙平台），实现物理设备数据在虚拟环境中的映射。

2. 开发环境准备
在开始编码前，请确保您的开发环境已按文档要求配置完毕：

虚拟机: 安装并运行Debian 11虚拟机。

交叉编译环境: 在虚拟机中安装用于ARM平台的交叉编译工具链及必要的开发库，特别是libgpiod-dev:armhf、libmosquitto-dev:armhf以及cJSON库。

硬件连接: 确保A7开发板已通过USB或网线与电脑连接，并且所有传感器、风扇模块已正确连接到开发板上。

网络连接: 确保A7开发板能够连接到互联网，以便与MQTT服务器通信。

3. 系统架构
您负责的部分是整个系统的数据采集和执行端，其工作流程如下：

传感器/执行器 -> A7开发板 (Linux Sysfs) -> C应用程序 -> JSON封装 -> MQTT库 -> MQTT服务器 (元宇宙)

4. 模块开发详解
我们将为每个硬件模块创建独立的C语言函数，以便于管理和调试。所有操作都将通过读写Linux系统下的sysfs文件接口来完成，这是在嵌入式Linux中操作硬件的常用方法。

目标: 读取环境的温度和湿度值。

硬件接口: I2C总线，对应iio:device0。

Sysfs路径: /sys/bus/iio/devices/iio:device0/

核心文件:

温度: in_temp_raw, in_temp_offset, in_temp_scale

湿度: in_humidityrelative_raw, in_humidityrelative_scale, in_humidityrelative_offset

数据处理: 根据文档P25提供的公式，计算实际温湿度值。

温度(℃) = (读取的in_temp_raw值 + 读取的in_temp_offset值) * 读取的in_temp_scale值 / 1000.0

湿度(%RH) = (读取的in_humidityrelative_raw值 + 读取的in_humidityrelative_offset值) * 读取的in_humidityrelative_scale值 / 1000.0

实现伪代码:

code
C
// file_utils.h (需要一个通用的文件读写函数)
// int read_sysfs_float(const char* path, float* value);

// temp_humi_sensor.c
#include "file_utils.h"

int get_temperature(float* temp) {
    float raw, offset, scale;
    read_sysfs_float("/sys/bus/iio/devices/iio:device0/in_temp_raw", &raw);
    read_sysfs_float("/sys/bus/iio/devices/iio:device0/in_temp_offset", &offset);
    read_sysfs_float("/sys/bus/iio/devices/iio:device0/in_temp_scale", &scale);
    *temp = (raw + offset) * scale / 1000.0;
    return 0; // Success
}
// ...类似地实现 get_humidity() 函数...
目标: 控制风扇的开启和关闭。

硬件接口: PWM，对应hwmon1。

Sysfs路径: /sys/class/hwmon/hwmon1/

核心文件: pwm1

控制逻辑:

开启风扇: 向pwm1文件写入一个非零值（例如 "100"）。

关闭风扇: 向pwm1文件写入 "0"。

实现伪代码:

code
C
// file_utils.h (需要一个通用的文件写入函数)
// int write_sysfs_string(const char* path, const char* value);

// fan_control.c
#include "file_utils.h"

const char* FAN_PATH = "/sys/class/hwmon/hwmon1/pwm1";

int fan_on() {
    return write_sysfs_string(FAN_PATH, "100");
}

int fan_off() {
    return write_sysfs_string(FAN_PATH, "0");
}
目标: 读取指定通道的电压值，作为电流的参考。

硬件接口: ADC，对应iio:device3。

Sysfs路径: /sys/bus/iio/devices/iio:device3/

核心文件: in_voltage1_raw (根据文档P26)

数据处理: 根据文档P26提供的公式，将读取的原始值转换为毫伏(mV)。

电压(mV) = 读取的in_voltage1_raw值 * 3300 / (1 << 16)

实现伪代码:

code
C
// voltage_sensor.c
#include "file_utils.h"

int get_voltage(float* voltage_mv) {
    float raw;
    read_sysfs_float("/sys/bus/iio/devices/iio:device3/in_voltage1_raw", &raw);
    *voltage_mv = raw * 3300.0 / 65536.0; // 65536 is 1 << 16
    return 0; // Success
}
目标: 读取当前环境光照强度。

硬件接口: I2C总线，对应iio:device1。

Sysfs路径: /sys/bus/iio/devices/iio:device1/

核心文件: in_illuminance_input

数据处理: 通常该文件直接提供勒克斯(lux)单位的值，可直接使用。

实现伪代码:

code
C
// light_sensor.c
#include "file_utils.h"

int get_illuminance(int* lux) {
    // 该文件值通常是整数，需要一个读取整数的通用函数
    return read_sysfs_int("/sys/bus/iio/devices/iio:device1/in_illuminance_input", lux);
}
5. 数据整合与MQTT通信 (最终配置)
服务器地址 (Host): mqtt.yyzlab.com.cn

端口 (Port): 1883

ClientID: 任意唯一的字符串 (例如 mqttx_5055659c 或您自定义的)。

用户名/密码: 无

SSL/TLS: 禁用

硬件数据发布主题 (Publish Topic): 1756266842788/AIOTSIM2Device (来自元宇宙配置图)

硬件控制订阅主题 (Subscribe Topic): 1756266842788/Device2AIOTSIM (来自元宇宙配置图)

现在，我们的C代码中的连接部分变得更加标准和简洁。

code
C
#include <mosquitto.h>
#include <stdio.h>
#include <unistd.h>
#include <string.h>

// --- 从MQTTX配置截图中获取的最终参数 ---
#define MQTT_HOST "mqtt.yyzlab.com.cn"
#define MQTT_PORT 1883  // 使用标准的1883端口
#define CLIENT_ID "kmlg_a7_board_01" // 建议使用一个有辨识度的、唯一的ID

// --- Topic仍然使用元宇宙配置中提供的 ---
#define PUB_TOPIC "1756266842788/AIOTSIM2Device"
#define SUB_TOPIC "1756266842788/Device2AIOTSIM"

struct mosquitto *mosq = NULL;

// 消息回调函数 (on_message_callback) ...
// [此部分代码与上一版相同]
void on_message_callback(struct mosquitto *mosq, void *userdata, const struct mosquitto_message *message) {
    if (message->payloadlen) {
        printf("Received command on topic %s: %s\n", message->topic, (char *)message->payload);
        
        // 解析来自云端的指令
        // ...
    }
}


int main() {
    mosquitto_lib_init();
    
    // 1. 创建客户端实例
    mosq = mosquitto_new(CLIENT_ID, true, NULL);
    if (!mosq) {
        fprintf(stderr, "Error: Out of memory.\n");
        return 1;
    }

    // 2. 设置消息回调
    mosquitto_message_callback_set(mosq, on_message_callback);
    
    // 3. 连接服务器 (使用更新后的 HOST 和 PORT)
    int rc = mosquitto_connect(mosq, MQTT_HOST, MQTT_PORT, 60);
    if (rc != MOSQ_ERR_SUCCESS) {
        fprintf(stderr, "Unable to connect to MQTT broker: %s\n", mosquitto_strerror(rc));
        return 1;
    }
    
    // 4. 订阅控制主题
    mosquitto_subscribe(mosq, NULL, SUB_TOPIC, 0);

    // 5. 启动网络循环线程
    mosquitto_loop_start(mosq);

    printf("Successfully connected to %s:%d\n", MQTT_HOST, MQTT_PORT);

    // 主循环，用于周期性发布数据
    while(1) {
        // ... (此处为获取传感器数据、封装JSON、发布消息的逻辑，与上一版相同) ...
        
        // 模拟数据
        float temperature = 26.1; 
        
        // 打包为JSON
        // char* json_payload = package_data_to_json(...);

        // 发布
        // mosquitto_publish(mosq, NULL, PUB_TOPIC, ...);
        // printf("Published: %s\n", json_payload);
        // free(json_payload);
        
        printf("Publishing mock data...\n");
        mosquitto_publish(mosq, NULL, PUB_TOPIC, strlen("{\"temp\":26.1}"), "{\"temp\":26.1}", 0, false);
        
        sleep(5);
    }

    mosquitto_lib_cleanup();
    return 0;
}```



6. 编译与部署
编写CMakeLists.txt: 参考文档P28，你需要创建一个CMakeLists.txt文件来管理项目。

code
Cmake
cmake_minimum_required(VERSION 3.10.0)
project(smart_home_device C)

# 添加所有你的.c源文件
add_executable(my_app main.c temp_humi_sensor.c fan_control.c ...)

# 链接必要的库
target_link_libraries(my_app mosquitto cjson)
交叉编译: 在虚拟机的项目目录下，使用cmake和make进行编译，生成可在A7开发板上运行的可执行文件my_app。

部署: 使用scp命令将my_app文件从虚拟机传输到A7开发板上。

运行: 在A7开发板的终端中，给my_app添加执行权限 (chmod +x my_app)，然后运行 (./my_app)。

调试: 观察程序的打印输出和元宇宙平台的数据更新情况，进行调试。

#### **总结与下一步**

*   **配置简化:** 我们现在有了明确且简单的连接配置，这将大大降低您在开发和调试网络连接部分时可能遇到的困难。
*   **任务不变:** 您的核心任务——编写传感器数据读取函数、风扇控制函数、以及将数据封装成JSON——保持不变。
*   **代码聚焦:** 您现在可以完全聚焦于硬件操作和数据处理，然后将结果填入上面这个稳定可靠的MQTT框架中即可。

请使用这份最终版的配置和代码框架来完成您的项目。祝您编码顺利！